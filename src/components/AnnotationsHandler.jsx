import { useState, useCallback } from 'react'
import { v4 as uuidv4 } from 'uuid'

// Custom hook for undo/redo functionality
const useUndoRedo = (maxHistorySize = 5) => {
  const [state, setState] = useState({
    history: [],
    currentIndex: -1
  })

  const pushToHistory = useCallback((newState) => {
    setState(prev => {
      // Remove any future history if we're not at the end
      const newHistory = prev.history.slice(0, prev.currentIndex + 1)
      // Add new state
      newHistory.push(newState)

      // Keep only the last maxHistorySize items
      if (newHistory.length > maxHistorySize) {
        newHistory.shift()
        return {
          history: newHistory,
          currentIndex: maxHistorySize - 1
        }
      } else {
        return {
          history: newHistory,
          currentIndex: newHistory.length - 1
        }
      }
    })
  }, [maxHistorySize])

  const undo = useCallback(() => {
    if (state.currentIndex > 0) {
      const newIndex = state.currentIndex - 1
      setState(prev => ({
        ...prev,
        currentIndex: newIndex
      }))
      return state.history[newIndex]
    }
    return null
  }, [state.currentIndex, state.history])

  const redo = useCallback(() => {
    if (state.currentIndex < state.history.length - 1) {
      const newIndex = state.currentIndex + 1
      setState(prev => ({
        ...prev,
        currentIndex: newIndex
      }))
      return state.history[newIndex]
    }
    return null
  }, [state.currentIndex, state.history])

  const canUndo = state.currentIndex > 0
  const canRedo = state.currentIndex < state.history.length - 1

  return { pushToHistory, undo, redo, canUndo, canRedo }
}

export const useAnnotationsHandler = (currentPdfIndex, currentPageIndex, customOverlapHandler = null) => {
  const [annotations, setAnnotations] = useState({}) // Annotations grouped by PDF index and page
  const [drawingMode, setDrawingMode] = useState('rectangle') // 'rectangle', 'polygon', or 'hand'
  const [currentAnnotation, setCurrentAnnotation] = useState(null)
  const [selectedAnnotations, setSelectedAnnotations] = useState([]) // Changed to array for multi-selection
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [copiedAnnotations, setCopiedAnnotations] = useState([]) // Changed to array for multi-copy
  const [polygonPoints, setPolygonPoints] = useState([])
  const [rectangleStartPoint, setRectangleStartPoint] = useState(null)

  // Initialize undo/redo functionality
  const { pushToHistory, undo, redo, canUndo, canRedo } = useUndoRedo(5)

  // Get current PDF annotations for current page
  const getCurrentAnnotations = useCallback(() => {
    const key = `${currentPdfIndex}-${currentPageIndex}`
    return annotations[key] || []
  }, [annotations, currentPdfIndex, currentPageIndex])

  // Update annotations for current PDF and page
  const updateCurrentAnnotations = useCallback((newAnnotations, skipHistory = false) => {
    const key = `${currentPdfIndex}-${currentPageIndex}`

    // Save current state to history before making changes (unless skipping)
    if (!skipHistory) {
      const currentState = {
        annotations: { ...annotations },
        pdfIndex: currentPdfIndex,
        pageIndex: currentPageIndex,
        timestamp: Date.now()
      }
      pushToHistory(currentState)
    }

    setAnnotations(prev => ({
      ...prev,
      [key]: newAnnotations
    }))
  }, [currentPdfIndex, currentPageIndex, annotations, pushToHistory])

  // Multi-selection helper functions
  const isAnnotationSelected = useCallback((annotation) => {
    return selectedAnnotations.some(selected => selected.id === annotation.id)
  }, [selectedAnnotations])

  const addToSelection = useCallback((annotation) => {
    if (!isAnnotationSelected(annotation)) {
      setSelectedAnnotations(prev => [...prev, annotation])
    }
  }, [isAnnotationSelected])

  const removeFromSelection = useCallback((annotation) => {
    setSelectedAnnotations(prev => prev.filter(selected => selected.id !== annotation.id))
  }, [])

  const toggleSelection = useCallback((annotation) => {
    if (isAnnotationSelected(annotation)) {
      removeFromSelection(annotation)
    } else {
      addToSelection(annotation)
    }
  }, [isAnnotationSelected, addToSelection, removeFromSelection])

  const clearSelection = useCallback(() => {
    setSelectedAnnotations([])
  }, [])

  const selectSingle = useCallback((annotation) => {
    setSelectedAnnotations([annotation])
  }, [])

  // Get primary selected annotation (first in selection for compatibility)
  const getPrimarySelection = useCallback(() => {
    return selectedAnnotations.length > 0 ? selectedAnnotations[0] : null
  }, [selectedAnnotations])

  // Check if point is inside rectangle
  const isPointInRectangle = useCallback((point, rect) => {
    return point.x >= rect.x &&
           point.x <= rect.x + rect.width &&
           point.y >= rect.y &&
           point.y <= rect.y + rect.height
  }, [])

  // Check if point is inside polygon using ray casting algorithm
  const isPointInPolygon = useCallback((point, polygon) => {
    let inside = false
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      if (((polygon[i].y > point.y) !== (polygon[j].y > point.y)) &&
          (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
        inside = !inside
      }
    }
    return inside
  }, [])

  // Find annotation at given point
  const findAnnotationAtPoint = useCallback((point) => {
    const currentAnnotations = getCurrentAnnotations()
    for (let i = currentAnnotations.length - 1; i >= 0; i--) {
      const annotation = currentAnnotations[i]

      if (annotation.type === 'rectangle') {
        if (isPointInRectangle(point, annotation)) {
          return annotation
        }
      } else if (annotation.type === 'polygon') {
        if (isPointInPolygon(point, annotation.points)) {
          return annotation
        }
      }
    }
    return null
  }, [getCurrentAnnotations, isPointInRectangle, isPointInPolygon])

  // Check if two rectangles overlap (with small tolerance for edge touching)
  const doRectanglesOverlap = useCallback((rect1, rect2) => {
    const tolerance = 1 // Small tolerance to avoid false positives for edge touching
    return !(rect1.x + rect1.width <= rect2.x + tolerance ||
             rect2.x + rect2.width <= rect1.x + tolerance ||
             rect1.y + rect1.height <= rect2.y + tolerance ||
             rect2.y + rect2.height <= rect1.y + tolerance)
  }, [])

  // Check if rectangle overlaps with polygon
  const doesRectangleOverlapPolygon = useCallback((rect, polygon) => {
    // Check if any corner of rectangle is inside polygon
    const corners = [
      { x: rect.x, y: rect.y },
      { x: rect.x + rect.width, y: rect.y },
      { x: rect.x, y: rect.y + rect.height },
      { x: rect.x + rect.width, y: rect.y + rect.height }
    ]

    for (const corner of corners) {
      if (isPointInPolygon(corner, polygon.points)) {
        return true
      }
    }

    // Check if any polygon point is inside rectangle
    for (const point of polygon.points) {
      if (isPointInRectangle(point, rect)) {
        return true
      }
    }

    return false
  }, [isPointInPolygon, isPointInRectangle])

  // Check if two polygons overlap (simplified check)
  const doPolygonsOverlap = useCallback((poly1, poly2) => {
    // Check if any point of poly1 is inside poly2
    for (const point of poly1.points) {
      if (isPointInPolygon(point, poly2.points)) {
        return true
      }
    }

    // Check if any point of poly2 is inside poly1
    for (const point of poly2.points) {
      if (isPointInPolygon(point, poly1.points)) {
        return true
      }
    }

    return false
  }, [isPointInPolygon])

  // Check for overlaps with existing annotations
  const checkForOverlaps = useCallback((newAnnotation) => {
    const currentAnnotations = getCurrentAnnotations()
    const overlappingAnnotations = []

    for (const existingAnnotation of currentAnnotations) {
      if (existingAnnotation.id === newAnnotation.id) continue

      let hasOverlap = false

      if (newAnnotation.type === 'rectangle' && existingAnnotation.type === 'rectangle') {
        hasOverlap = doRectanglesOverlap(newAnnotation, existingAnnotation)
      } else if (newAnnotation.type === 'rectangle' && existingAnnotation.type === 'polygon') {
        hasOverlap = doesRectangleOverlapPolygon(newAnnotation, existingAnnotation)
      } else if (newAnnotation.type === 'polygon' && existingAnnotation.type === 'rectangle') {
        hasOverlap = doesRectangleOverlapPolygon(existingAnnotation, newAnnotation)
      } else if (newAnnotation.type === 'polygon' && existingAnnotation.type === 'polygon') {
        hasOverlap = doPolygonsOverlap(newAnnotation, existingAnnotation)
      }

      if (hasOverlap) {
        overlappingAnnotations.push(existingAnnotation)
      }
    }

    return overlappingAnnotations
  }, [getCurrentAnnotations, doRectanglesOverlap, doesRectangleOverlapPolygon, doPolygonsOverlap])

  // Create rectangle annotation (returns annotation without adding to list)
  const createRectangleAnnotation = useCallback((startPoint, endPoint, onAnnotationCreated) => {
    const startX = Math.min(startPoint.x, endPoint.x)
    const startY = Math.min(startPoint.y, endPoint.y)
    const width = Math.abs(endPoint.x - startPoint.x)
    const height = Math.abs(endPoint.y - startPoint.y)

    // Only create rectangle if it has meaningful dimensions
    if (width > 5 && height > 5) {
      const currentAnnotations = getCurrentAnnotations()
      const annotationNumber = currentAnnotations.length + 1
      const newAnnotation = {
        id: uuidv4(),
        type: 'rectangle',
        pageIndex: currentPageIndex,
        x: startX,
        y: startY,
        width: width,
        height: height,
        color: '#ff0000',
        label: `Rectangle ${annotationNumber}`
      }

      // Check for overlaps
      const overlappingAnnotations = checkForOverlaps(newAnnotation)
      if (overlappingAnnotations.length > 0) {
        const overlappingLabels = overlappingAnnotations.map(ann => ann.label || `${ann.type} ${ann.id.slice(0, 8)}`).join(', ')

        if (customOverlapHandler) {
          // Use custom overlap handler (toast-based)
          customOverlapHandler(
            overlappingLabels,
            () => {
              // Proceed with annotation creation
              if (onAnnotationCreated) {
                onAnnotationCreated(newAnnotation)
              } else {
                updateCurrentAnnotations([...currentAnnotations, newAnnotation])
              }
            },
            () => {
              // Cancel annotation creation
              return null
            }
          )
          return null // Don't proceed immediately, wait for user choice
        } else {
          // Fallback to window.confirm
          const proceed = window.confirm(
            `Warning: This rectangle overlaps with existing annotation(s): ${overlappingLabels}\n\n` +
            `In this application, no area should be shared by more than one annotation. ` +
            `Do you want to create this overlapping annotation anyway?`
          )

          if (!proceed) {
            return null
          }
        }
      }

      // If callback provided, use it (for room dropdown), otherwise add directly
      if (onAnnotationCreated) {
        onAnnotationCreated(newAnnotation)
      } else {
        updateCurrentAnnotations([...currentAnnotations, newAnnotation])
      }
      return newAnnotation
    }
    return null
  }, [currentPageIndex, getCurrentAnnotations, updateCurrentAnnotations, checkForOverlaps])

  // Finish polygon annotation
  const finishPolygon = useCallback((onAnnotationCreated) => {
    if (currentAnnotation && polygonPoints.length >= 3) {
      // Check for overlaps
      const overlappingAnnotations = checkForOverlaps(currentAnnotation)
      if (overlappingAnnotations.length > 0) {
        const overlappingLabels = overlappingAnnotations.map(ann => ann.label || `${ann.type} ${ann.id.slice(0, 8)}`).join(', ')

        if (customOverlapHandler) {
          // Use custom overlap handler (toast-based)
          customOverlapHandler(
            overlappingLabels,
            () => {
              // Proceed with annotation creation
              if (onAnnotationCreated) {
                onAnnotationCreated(currentAnnotation)
              } else {
                updateCurrentAnnotations([...getCurrentAnnotations(), currentAnnotation])
              }
              setCurrentAnnotation(null)
              setPolygonPoints([])
            },
            () => {
              // Cancel annotation creation
              setCurrentAnnotation(null)
              setPolygonPoints([])
            }
          )
          return false // Don't proceed immediately, wait for user choice
        } else {
          // Fallback to window.confirm
          const proceed = window.confirm(
            `Warning: This polygon overlaps with existing annotation(s): ${overlappingLabels}\n\n` +
            `In this application, no area should be shared by more than one annotation. ` +
            `Do you want to create this overlapping annotation anyway?`
          )

          if (!proceed) {
            setCurrentAnnotation(null)
            setPolygonPoints([])
            return false
          }
        }
      }

      // If callback provided, use it (for room dropdown), otherwise add directly
      if (onAnnotationCreated) {
        onAnnotationCreated(currentAnnotation)
      } else {
        updateCurrentAnnotations([...getCurrentAnnotations(), currentAnnotation])
      }

      setCurrentAnnotation(null)
      setPolygonPoints([])
      return true
    }
    return false
  }, [currentAnnotation, polygonPoints, getCurrentAnnotations, updateCurrentAnnotations, checkForOverlaps])

  // Copy annotation functionality - now supports multiple annotations
  const copyAnnotation = useCallback((annotation) => {
    if (annotation) {
      // Single annotation copy
      setCopiedAnnotations([{ ...annotation, id: uuidv4() }])
    } else if (selectedAnnotations.length > 0) {
      // Copy all selected annotations
      setCopiedAnnotations(selectedAnnotations.map(ann => ({ ...ann, id: uuidv4() })))
    }
  }, [selectedAnnotations])

  const copySelectedAnnotations = useCallback(() => {
    if (selectedAnnotations.length > 0) {
      setCopiedAnnotations(selectedAnnotations.map(ann => ({ ...ann, id: uuidv4() })))
    }
  }, [selectedAnnotations])

  // Delete annotation - now supports multiple annotations
  const deleteAnnotation = useCallback((annotationId) => {
    try {
      const currentAnnotations = getCurrentAnnotations()
      if (!Array.isArray(currentAnnotations)) {
        console.error('getCurrentAnnotations() did not return an array:', currentAnnotations)
        return
      }

      const updatedAnnotations = currentAnnotations.filter(ann => ann.id !== annotationId)
      updateCurrentAnnotations(updatedAnnotations)

      // Remove from selection if it was selected
      setSelectedAnnotations(prev => prev.filter(selected => selected.id !== annotationId))
    } catch (error) {
      console.error('Error deleting annotation:', error)
    }
  }, [getCurrentAnnotations, updateCurrentAnnotations])

  const deleteSelectedAnnotations = useCallback(() => {
    try {
      const currentAnnotations = getCurrentAnnotations()
      if (!Array.isArray(currentAnnotations)) {
        console.error('getCurrentAnnotations() did not return an array:', currentAnnotations)
        return
      }

      const selectedIds = new Set(selectedAnnotations.map(ann => ann.id))
      const updatedAnnotations = currentAnnotations.filter(ann => !selectedIds.has(ann.id))
      updateCurrentAnnotations(updatedAnnotations)
      clearSelection()
    } catch (error) {
      console.error('Error deleting selected annotations:', error)
    }
  }, [getCurrentAnnotations, updateCurrentAnnotations, selectedAnnotations, clearSelection])

  // Update annotation label
  const updateAnnotationLabel = useCallback((annotationId, newLabel) => {
    const currentAnnotations = getCurrentAnnotations()
    const updatedAnnotations = currentAnnotations.map(annotation =>
      annotation.id === annotationId
        ? { ...annotation, label: newLabel }
        : annotation
    )
    updateCurrentAnnotations(updatedAnnotations)

    // Update selected annotations if any match the edited one
    setSelectedAnnotations(prev => prev.map(selected =>
      selected.id === annotationId
        ? { ...selected, label: newLabel }
        : selected
    ))
  }, [getCurrentAnnotations, updateCurrentAnnotations])

  // Clear current drawing state
  const clearDrawingState = useCallback(() => {
    setCurrentAnnotation(null)
    setPolygonPoints([])
    setRectangleStartPoint(null)
    clearSelection()
    setIsDragging(false)
  }, [clearSelection])

  // Undo functionality
  const undoAnnotations = useCallback(() => {
    const previousState = undo()
    if (previousState) {
      // Restore the previous annotations state directly without triggering history
      setAnnotations(previousState.annotations)
      // Clear any current selections since they might not be valid anymore
      clearSelection()
    }
  }, [undo, clearSelection])

  // Redo functionality
  const redoAnnotations = useCallback(() => {
    const nextState = redo()
    if (nextState) {
      // Restore the next annotations state directly without triggering history
      setAnnotations(nextState.annotations)
      // Clear any current selections since they might not be valid anymore
      clearSelection()
    }
  }, [redo, clearSelection])

  return {
    // State
    annotations,
    drawingMode,
    currentAnnotation,
    selectedAnnotations, // Changed from selectedAnnotation
    isDragging,
    dragOffset,
    copiedAnnotations, // Changed from copiedAnnotation
    polygonPoints,
    rectangleStartPoint,

    // Undo/Redo state
    canUndo,
    canRedo,

    // Actions
    setDrawingMode,
    setCurrentAnnotation,
    setSelectedAnnotations, // Changed from setSelectedAnnotation
    setIsDragging,
    setDragOffset,
    setCopiedAnnotations, // Changed from setCopiedAnnotation
    setPolygonPoints,
    setRectangleStartPoint,
    getCurrentAnnotations,
    updateCurrentAnnotations,
    findAnnotationAtPoint,
    createRectangleAnnotation,
    finishPolygon,
    copyAnnotation,
    copySelectedAnnotations,
    deleteAnnotation,
    deleteSelectedAnnotations,
    updateAnnotationLabel,
    checkForOverlaps,
    clearDrawingState,

    // Multi-selection helpers
    isAnnotationSelected,
    addToSelection,
    removeFromSelection,
    toggleSelection,
    clearSelection,
    selectSingle,
    getPrimarySelection,

    // Undo/Redo actions
    undoAnnotations,
    redoAnnotations
  }
}
