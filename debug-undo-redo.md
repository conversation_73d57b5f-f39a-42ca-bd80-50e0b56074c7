# Debug Steps for Undo/Redo Fix

## Issue Identified
The problem was in the `useUndoRedo` hook where state updates were not properly synchronized, causing multiple entries to be removed during undo operations.

## Changes Made

### 1. Fixed `useUndoRedo` Hook
- **Before**: Used separate `history` and `currentIndex` state variables
- **After**: Combined into single state object to ensure atomic updates
- **Benefit**: Eliminates race conditions between state updates

### 2. Improved State Management
```javascript
// Old approach (problematic)
const [history, setHistory] = useState([])
const [currentIndex, setCurrentIndex] = useState(-1)

// New approach (fixed)
const [state, setState] = useState({
  history: [],
  currentIndex: -1
})
```

## Testing Steps

1. **Load a PDF and create annotations**:
   - Create 3-4 rectangle annotations
   - Each creation should add one entry to history

2. **Test Undo**:
   - Press Ctrl+Z once → Should remove only the last annotation
   - Press Ctrl+Z again → Should remove only the second-to-last annotation
   - Continue until all annotations are undone

3. **Test Redo**:
   - Press Ctrl+Y → Should restore the last undone annotation
   - Continue pressing Ctrl+Y to restore all annotations

4. **Test History Limit**:
   - Create more than 5 annotations
   - Verify only the last 5 changes can be undone

## Expected Behavior (Fixed)
- ✅ Each undo operation removes exactly one annotation
- ✅ Each redo operation restores exactly one annotation  
- ✅ History is properly maintained with atomic state updates
- ✅ No duplicate entries or skipped entries in history

## Root Cause
The issue was caused by React's asynchronous state updates. When `pushToHistory` was called, it used stale values of `currentIndex` due to closure issues, causing incorrect history management.

The fix ensures all history state is updated atomically in a single `setState` call.
